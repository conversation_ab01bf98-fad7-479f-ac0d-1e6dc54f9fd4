{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-12T02:57:04.273Z", "updatedAt": "2025-08-12T02:57:04.279Z", "resourceCount": 9}, "resources": [{"id": "ba", "source": "project", "protocol": "role", "name": "Ba 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ba/ba.role.md", "metadata": {"createdAt": "2025-08-12T02:57:04.274Z", "updatedAt": "2025-08-12T02:57:04.274Z", "scannedAt": "2025-08-12T02:57:04.274Z", "path": "role/ba/ba.role.md"}}, {"id": "cm", "source": "project", "protocol": "role", "name": "Cm 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/cm/cm.role.md", "metadata": {"createdAt": "2025-08-12T02:57:04.275Z", "updatedAt": "2025-08-12T02:57:04.275Z", "scannedAt": "2025-08-12T02:57:04.275Z", "path": "role/cm/cm.role.md"}}, {"id": "context-management", "source": "project", "protocol": "execution", "name": "Context Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/cm/execution/context-management.execution.md", "metadata": {"createdAt": "2025-08-12T02:57:04.275Z", "updatedAt": "2025-08-12T02:57:04.275Z", "scannedAt": "2025-08-12T02:57:04.275Z", "path": "role/cm/execution/context-management.execution.md"}}, {"id": "context-analysis", "source": "project", "protocol": "thought", "name": "Context Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/cm/thought/context-analysis.thought.md", "metadata": {"createdAt": "2025-08-12T02:57:04.276Z", "updatedAt": "2025-08-12T02:57:04.276Z", "scannedAt": "2025-08-12T02:57:04.276Z", "path": "role/cm/thought/context-analysis.thought.md"}}, {"id": "information-integration", "source": "project", "protocol": "thought", "name": "Information Integration 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/cm/thought/information-integration.thought.md", "metadata": {"createdAt": "2025-08-12T02:57:04.276Z", "updatedAt": "2025-08-12T02:57:04.276Z", "scannedAt": "2025-08-12T02:57:04.276Z", "path": "role/cm/thought/information-integration.thought.md"}}, {"id": "ld", "source": "project", "protocol": "role", "name": "Ld 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ld/ld.role.md", "metadata": {"createdAt": "2025-08-12T02:57:04.278Z", "updatedAt": "2025-08-12T02:57:04.278Z", "scannedAt": "2025-08-12T02:57:04.278Z", "path": "role/ld/ld.role.md"}}, {"id": "pl", "source": "project", "protocol": "role", "name": "Pl 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pl/pl.role.md", "metadata": {"createdAt": "2025-08-12T02:57:04.278Z", "updatedAt": "2025-08-12T02:57:04.278Z", "scannedAt": "2025-08-12T02:57:04.278Z", "path": "role/pl/pl.role.md"}}, {"id": "pm", "source": "project", "protocol": "role", "name": "Pm 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pm/pm.role.md", "metadata": {"createdAt": "2025-08-12T02:57:04.278Z", "updatedAt": "2025-08-12T02:57:04.278Z", "scannedAt": "2025-08-12T02:57:04.278Z", "path": "role/pm/pm.role.md"}}, {"id": "sa", "source": "project", "protocol": "role", "name": "Sa 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/sa/sa.role.md", "metadata": {"createdAt": "2025-08-12T02:57:04.279Z", "updatedAt": "2025-08-12T02:57:04.279Z", "scannedAt": "2025-08-12T02:57:04.279Z", "path": "role/sa/sa.role.md"}}], "stats": {"totalResources": 9, "byProtocol": {"role": 6, "execution": 1, "thought": 2}, "bySource": {"project": 9}}}