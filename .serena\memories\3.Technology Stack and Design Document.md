# 3. 技术栈与设计文档

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | 2025-08-12 12:07:29 +08:00 | SA | 初始创建 |
| 1.1  | 2025-08-12 12:07:29 +08:00 | SA | 基于方案B进行技术栈确定和系统架构设计 |

---

## 0. 架构基础回顾 (模式3基于模式2产出)

[INTERNAL_ACTION: Reading from Serena Memory - .serena\memories\2.Solution Architecture and Innovation Document.md via Serena MCP for technology and architecture design foundation.]

### 0.1 前序文档关键信息提取
| 信息类别 | 关键内容 | 技术影响 | 实现约束 |
| :--- | :--- | :--- | :--- |
| 确定的解决方案 | PyQt5重新设计UI方案 | 技术栈确定为PyQt5+ZMotion | 必须保持现有后端逻辑 |
| 技术方向指导 | 实用简洁UI，代码实现 | UI通过代码实现，非设计器 | 显示精度小数点后3位 |
| 架构约束 | 三层架构(UI/业务/硬件) | 模块化设计，职责分离 | 5周开发周期约束 |
| 用户确认要点 | 核心功能优先开发 | 配置保存/状态指示后续开发 | 基于现有ZMotion架构 |

### 0.2 解决方案完整性验证
- [x] 解决方案描述清晰完整
- [x] 用户沟通记录详细
- [x] 技术约束明确可执行
- [x] 创新要求具体可实现
- [x] 知识库资源已整理完毕

## 1. 技术栈选择与论证

[INTERNAL_ACTION: Using AugmentContextEngine to gather comprehensive information for technology stack analysis.]

### 1.1 核心技术栈确定

| 类别 | 技术/工具 | 版本 | 选择理由 | 方案约束匹配 |
| :--- | :--- | :--- | :--- | :--- |
| **GUI框架** | PyQt5 | 5.15+ | 用户明确要求，社区资源丰富 | 满足UI重新设计要求 |
| **编程语言** | Python | 3.8+ | 现有代码基础，团队熟悉 | 兼容现有ZMotion接口 |
| **控制器接口** | ZMotion zmcdll | 现有版本 | 复用现有成熟接口 | 满足IP连接要求 |
| **开发工具** | VS Code | 最新版 | 团队标准开发环境 | 支持Python和PyQt5开发 |
| **版本控制** | Git | 2.30+ | 项目管理标准 | 满足开发规范要求 |

### 1.2 技术栈选择论证

#### PyQt5 vs PySide6 最终决策
**选择PyQt5的关键理由**：
- ✅ **用户明确要求**：用户明确指定使用PyQt5
- ✅ **社区资源丰富**：更多文档、示例和第三方控件
- ✅ **API成熟稳定**：长期稳定的API，兼容性好
- ✅ **迁移成本可控**：从PySide6迁移工作量可控

**迁移策略**：
```python
# PySide6 → PyQt5 关键差异
# 导入语句更新
from PySide6.QtWidgets import QApplication  # 旧
from PyQt5.QtWidgets import QApplication    # 新

# UI加载方式更新
from PySide6.QtUiTools import QUiLoader     # 旧
from PyQt5 import uic                       # 新
```

### 1.3 开发环境配置

#### 1.3.1 Python环境配置
```powershell
# 检查Python版本（要求3.8+）
python --version

# 创建虚拟环境
python -m venv venv_zmotiondof5

# 激活虚拟环境
.\venv_zmotiondof5\Scripts\Activate.ps1

# 安装核心依赖
pip install PyQt5==5.15.7
pip install PyQt5-tools==********
```

#### 1.3.2 开发工具配置
```powershell
# 安装VS Code扩展
code --install-extension ms-python.python
code --install-extension ms-python.pylint
code --install-extension ms-python.black-formatter
```

## 2. 系统架构设计

### 2.1 整体架构图
```
┌─────────────────────────────────────────────────────────┐
│                    应用层 (PyQt5 UI)                    │
├─────────────────────────────────────────────────────────┤
│  主窗口     │  参数配置   │  状态显示   │  控制组件    │
│  MainWindow │  ConfigDialog│ StatusPanel │ ControlPanel │
├─────────────────────────────────────────────────────────┤
│                    业务逻辑层                            │
├─────────────────────────────────────────────────────────┤
│  运动控制器  │  轴管理器   │  配置管理器  │  状态管理器  │
│ MotionCtrl  │ AxisManager │ ConfigMgr   │ StatusMgr   │
├─────────────────────────────────────────────────────────┤
│                    硬件接口层                            │
├─────────────────────────────────────────────────────────┤
│              ZMotion控制器接口 (复用现有)                │
│                zmcdll.zauxdllPython                     │
├─────────────────────────────────────────────────────────┤
│                    硬件层                               │
├─────────────────────────────────────────────────────────┤
│                  ZMotion控制器                          │
│              5轴运动控制硬件系统                         │
└─────────────────────────────────────────────────────────┘
```

### 2.2 文件结构设计
```
zmotiondof5/
├── main.py                 # 应用程序入口
├── ui/                     # UI层
│   ├── __init__.py
│   ├── main_window.py      # 主窗口类
│   ├── config_dialog.py    # 参数配置对话框
│   └── widgets/            # UI组件
│       ├── __init__.py
│       ├── axis_control.py # 轴控制组件
│       ├── status_display.py # 状态显示组件
│       └── connection_panel.py # 连接面板组件
├── core/                   # 业务逻辑层
│   ├── __init__.py
│   ├── motion_controller.py # 运动控制核心逻辑
│   ├── axis_manager.py     # 轴管理器
│   └── config_manager.py   # 配置管理器
├── zmcdll/                 # 硬件接口层(复用现有)
│   ├── __init__.py
│   ├── zauxdllPython.py
│   └── zauxdll.dll
└── resources/              # 资源文件
    ├── icons/              # 图标资源
    └── styles/             # 样式文件(可选)
```

### 2.3 核心类设计

#### 主窗口类 (MainWindow)
```python
class MainWindow(QMainWindow):
    """5轴运动控制主窗口"""
    def __init__(self):
        super().__init__()
        self.motion_controller = MotionController()
        self.axis_manager = AxisManager()
        self.setup_ui()
        self.setup_connections()
        self.setup_timer()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央窗口部件
        # 创建连接控制区域
        # 创建位置显示区域
        # 创建轴控制区域
        # 创建单步控制区域
        
    def setup_connections(self):
        """设置信号连接"""
        # 连接按钮信号
        # 连接定时器信号
        
    def setup_timer(self):
        """设置状态更新定时器"""
        # 100ms更新频率
```

#### 轴管理器 (AxisManager)
```python
class AxisManager:
    """5轴管理器"""
    def __init__(self):
        self.axes = {
            'X': {'type': 'linear', 'unit': 'mm', 'precision': 3, 'axis_num': 0},
            'Y': {'type': 'linear', 'unit': 'mm', 'precision': 3, 'axis_num': 1},
            'Z': {'type': 'linear', 'unit': 'mm', 'precision': 3, 'axis_num': 2},
            'Rx': {'type': 'rotary', 'unit': '°', 'precision': 3, 'axis_num': 3},
            'Ry': {'type': 'rotary', 'unit': '°', 'precision': 3, 'axis_num': 4}
        }
        
    def get_axis_position(self, axis_name):
        """获取轴位置"""
        
    def move_axis_to_position(self, axis_name, position):
        """移动轴到指定位置"""
        
    def move_all_axes_to_positions(self, positions):
        """多轴协调运动"""
        
    def home_all_axes(self):
        """所有轴回零"""
```

## 3. 用户界面设计

### 3.1 主窗口布局设计
```
┌─────────────────────────────────────────────────────────┐
│                    5轴运动控制系统                        │
├─────────────────────────────────────────────────────────┤
│  连接控制区域                                            │
│  IP地址: [************* ▼] [扫描] [连接] [断开]          │
│  状态: [已连接] 控制器: [ZMotion-MC4000]                 │
├─────────────────────────────────────────────────────────┤
│  位置信息显示区域 (实时更新)                              │
│  X: 5.200mm   Y: 3.100mm   Z: 1.500mm                  │
│  Rx: 0.500°   Ry: 0.300°                               │
├─────────────────────────────────────────────────────────┤
│  轴控制区域                                              │
│  目标位置设置:                                           │
│  X: [_____] mm  Y: [_____] mm  Z: [_____] mm            │
│  Rx: [_____] °  Ry: [_____] °                           │
│  [运动到目标位置] [停止运动] [复位(回零)]                 │
├─────────────────────────────────────────────────────────┤
│  单步控制区域                                            │
│  步长设置:                                               │
│  X: [0.001] mm  Y: [0.001] mm  Z: [0.001] mm            │
│  Rx: [0.001] °  Ry: [0.001] °                           │
│                                                         │
│  X轴: [+] [-]  Y轴: [+] [-]  Z轴: [+] [-]                │
│  Rx轴: [+] [-]  Ry轴: [+] [-]                           │
├─────────────────────────────────────────────────────────┤
│  [参数配置] [关于] [帮助]                                 │
└─────────────────────────────────────────────────────────┘
```

### 3.2 参数配置对话框设计
```
┌─────────────────────────────────────────┐
│            轴参数配置                    │
├─────────────────────────────────────────┤
│ 选择轴: [X轴 ▼]                         │
├─────────────────────────────────────────┤
│ UNITS (脉冲当量):    [1000.000]         │
│ 起始速度 (mm/s):     [0.000   ]         │
│ 运行速度 (mm/s):     [10.000  ]         │
│ 加速度 (mm/s²):      [100.000 ]         │
│ 减速度 (mm/s²):      [100.000 ]         │
│ S曲线时间 (ms):      [20.000  ]         │
├─────────────────────────────────────────┤
│ [应用到轴] [重置为默认值]                │
│ [确定] [取消]                           │
└─────────────────────────────────────────┘
```

### 3.3 UI设计原则
- **实用简洁**: 界面布局清晰，功能分区明确
- **信息层次**: 重要信息突出显示，次要信息适当弱化
- **操作便捷**: 常用功能一键操作，减少操作步骤
- **状态反馈**: 实时显示系统状态和操作结果
- **错误处理**: 友好的错误提示和异常处理

### 3.4 UI实现技术细节

#### 3.4.1 PyQt5布局管理
```python
def setup_ui(self):
    """设置主窗口UI布局"""
    central_widget = QWidget()
    self.setCentralWidget(central_widget)
    
    # 主布局
    main_layout = QVBoxLayout(central_widget)
    
    # 连接控制区域
    connection_group = self.create_connection_group()
    main_layout.addWidget(connection_group)
    
    # 位置显示区域
    position_group = self.create_position_group()
    main_layout.addWidget(position_group)
    
    # 轴控制区域
    control_group = self.create_control_group()
    main_layout.addWidget(control_group)
    
    # 单步控制区域
    step_group = self.create_step_group()
    main_layout.addWidget(step_group)
```

#### 3.4.2 精度控制实现
```python
def format_position(self, value, precision=3):
    """格式化位置显示"""
    return f"{value:.{precision}f}"

def format_linear_position(self, value):
    """格式化线性轴位置 (mm)"""
    return f"{value:.3f} mm"
    
def format_rotary_position(self, value):
    """格式化旋转轴位置 (°)"""
    return f"{value:.3f}°"
```

## 4. 核心功能实现方案

### 4.1 连接管理功能
**功能描述**: 管理与ZMotion控制器的IP连接
**实现方案**:
- 复用现有的IP扫描和连接逻辑
- 优化连接状态显示和错误处理
- 添加自动重连机制

**关键代码结构**:
```python
class ConnectionManager:
    def scan_controllers(self):
        """扫描可用的控制器"""
        
    def connect_to_controller(self, ip_address):
        """连接到指定IP的控制器"""
        
    def disconnect_from_controller(self):
        """断开控制器连接"""
        
    def get_connection_status(self):
        """获取连接状态"""
```

### 4.2 5轴状态监测功能
**功能描述**: 实时显示5个轴的位置、状态和速度
**实现方案**:
- 100ms定时器更新轴状态
- 显示精度: 小数点后3位
- 状态包括: 位置、运动状态、当前速度

**关键代码结构**:
```python
class StatusMonitor:
    def __init__(self):
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(100)  # 100ms更新
        
    def update_status(self):
        """更新所有轴状态"""
        for axis_name in ['X', 'Y', 'Z', 'Rx', 'Ry']:
            position = self.get_axis_position(axis_name)
            self.update_position_display(axis_name, position)
```

### 4.3 多轴协调运动功能
**功能描述**: 5个轴同时运动到目标位置
**实现方案**:
- 使用ZMotion的多轴直线插补功能
- 支持运动过程中的停止操作
- 运动完成状态反馈

**关键代码结构**:
```python
class MultiAxisController:
    def move_to_positions(self, target_positions):
        """多轴协调运动到目标位置"""
        axis_list = [0, 1, 2, 3, 4]  # X, Y, Z, Rx, Ry
        position_list = [
            target_positions['X'],
            target_positions['Y'], 
            target_positions['Z'],
            target_positions['Rx'],
            target_positions['Ry']
        ]
        return self.zmc.ZAux_Direct_MoveAbs(5, axis_list, position_list)
```

### 4.4 单轴单步运动功能
**功能描述**: 每个轴的正向和负向单步运动
**实现方案**:
- 可配置的步长设置
- 立即执行的单步运动
- 支持连续点击操作

**关键代码结构**:
```python
class SingleStepController:
    def step_move(self, axis_name, direction, step_size):
        """单轴单步运动"""
        axis_num = self.axis_manager.get_axis_number(axis_name)
        current_pos = self.get_current_position(axis_num)
        target_pos = current_pos + (step_size * direction)
        return self.zmc.ZAux_Direct_Single_MoveAbs(axis_num, target_pos)
```

### 4.5 轴回零功能
**功能描述**: 所有轴回到零位置
**实现方案**:
- 一键回零操作
- 回零过程状态显示
- 回零完成确认

**关键代码结构**:
```python
class HomeController:
    def home_all_axes(self):
        """所有轴回零"""
        axis_list = [0, 1, 2, 3, 4]  # X, Y, Z, Rx, Ry
        zero_positions = [0.0, 0.0, 0.0, 0.0, 0.0]
        return self.zmc.ZAux_Direct_MoveAbs(5, axis_list, zero_positions)
```

## 5. 技术实现细节

### 5.1 PyQt5迁移实现
**迁移步骤**:
1. 更新导入语句: `from PySide6.QtWidgets` → `from PyQt5.QtWidgets`
2. 更新UI加载方式: `QUiLoader` → 纯代码实现
3. 适配API差异: 主要是信号连接语法的微调
4. 测试验证: 确保所有功能正常工作

**关键差异处理**:
```python
# PySide6 方式
from PySide6.QtWidgets import QApplication
from PySide6.QtUiTools import QUiLoader

# PyQt5 方式  
from PyQt5.QtWidgets import QApplication
# 使用纯代码实现UI，不使用uic
```

### 5.2 精度控制实现
**显示精度**: 小数点后3位
**实现方案**:
```python
def format_position(self, value, precision=3):
    """格式化位置显示"""
    return f"{value:.{precision}f}"

def format_linear_position(self, value):
    """格式化线性轴位置 (mm)"""
    return f"{value:.3f} mm"
    
def format_rotary_position(self, value):
    """格式化旋转轴位置 (°)"""
    return f"{value:.3f}°"
```

### 5.3 错误处理和异常管理
**错误处理策略**:
- 连接异常: 自动重试 + 用户提示
- 运动异常: 立即停止 + 状态恢复
- 参数异常: 输入验证 + 范围检查
- 系统异常: 日志记录 + 优雅降级

## 6. 接口定义

### 6.1 ZMotion控制器接口
**接口类型**: 硬件控制接口
**通信协议**: IP网络通信
**数据格式**: ZMotion专有协议

**关键接口方法**:
```python
# 连接管理
ZAux_OpenEth(ip_address)           # IP连接
ZAux_Close()                       # 断开连接
ZAux_SearchEthlist()               # IP扫描

# 轴状态查询
ZAux_Direct_GetDpos(axis_num)      # 获取当前位置
ZAux_Direct_GetIfIdle(axis_num)    # 获取轴状态
ZAux_Direct_GetSpeed(axis_num)     # 获取当前速度

# 轴运动控制
ZAux_Direct_Single_MoveAbs(axis_num, position)  # 单轴绝对运动
ZAux_Direct_MoveAbs(axis_count, axis_list, position_list)  # 多轴运动

# 轴参数设置
ZAux_Direct_SetUnits(axis_num, units)    # 设置脉冲当量
ZAux_Direct_SetSpeed(axis_num, speed)    # 设置速度
ZAux_Direct_SetAccel(axis_num, accel)    # 设置加速度
```

### 6.2 内部模块接口
**模块间通信**: Python类方法调用
**数据传递**: 字典和对象传递
**事件机制**: PyQt5信号槽机制

## 7. 递进输出 (为模式4项目规划提供技术基础)

### 7.1 为PL角色提供的关键信息
| 输出类别 | 具体内容 | 规划影响 | 优先级 |
| :--- | :--- | :--- | :--- |
| 技术栈清单 | PyQt5+Python+ZMotion完整技术栈 | 开发环境和工具要求明确 | 高 |
| 系统架构 | 三层架构详细设计 | 模块划分和依赖关系清晰 | 高 |
| 开发约束 | PyQt5迁移、精度控制、UI代码实现 | 开发顺序和技术难点识别 | 高 |
| 接口规范 | ZMotion接口和内部模块接口定义 | 集成测试和模块开发要求 | 中 |
| UI/UX设计 | 实用简洁的5轴控制界面设计 | 前端开发具体要求 | 中 |
| 核心功能 | 连接、监测、控制、配置功能实现方案 | 功能开发优先级和实现策略 | 高 |

### 7.2 技术实现指导
* **开发环境要求**: Python 3.8+ + PyQt5 5.15+ + VS Code开发环境
* **技术学习路径**: PyQt5 UI开发 → ZMotion接口集成 → 多轴控制算法
* **关键技术风险**: 
  - PyQt5迁移兼容性问题
  - 多轴控制稳定性问题
  - UI性能优化问题
* **性能基准**: 100ms状态更新频率，200ms UI响应时间

### 7.3 质量检查清单
- [x] 技术栈选择已完成并论证充分
- [x] 系统架构设计详细且可实现
- [x] UI/UX设计规范明确且符合用户要求
- [x] 核心功能实现方案具体且可操作
- [x] 接口定义完整且标准化
- [x] 技术风险已识别并有应对措施
- [x] 开发约束已明确传达
- [x] PyQt5迁移策略明确可行
- [x] 精度控制实现方案具体
- [x] 错误处理策略完整

### 7.4 后续阶段准备
* **任务分解准备**: 为PL角色提供详细的技术模块和功能点分解基础
* **开发顺序建议**:
  - 优先级1: PyQt5开发环境配置和迁移验证
  - 优先级2: 基础UI框架和连接功能开发
  - 优先级3: 5轴状态监测和显示功能
  - 优先级4: 多轴控制和单步控制功能
  - 优先级5: 参数配置功能(后续开发)
* **测试策略输入**: 为测试规划提供技术架构和接口测试要求

[INTERNAL_ACTION: Writing to Serena Memory - .serena\memories\3.Technology Stack and Design Document.md via Serena MCP with mcp.server_time timestamp with technology stack and system architecture design.]

**递进关系说明**: 本文档作为模式3的产出，基于模式2确定的解决方案，为模式4的项目规划与任务管理提供完整的技术架构基础，确保PL角色能够基于具体的技术选型和架构设计进行详细的项目规划。