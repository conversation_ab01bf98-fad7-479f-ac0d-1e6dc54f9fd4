#!/usr/bin/python
# coding:utf-8

"""
@author:ocean
@contact:<EMAIL>
@software:PyCharm
@file:main.py
@time:2023/3/13 14:32
"""
import sys
from PySide6.QtWidgets import QApplication
from Ui_Weiget import UiInterFace

if __name__ == "__main__":
    try:
        app = QApplication([])
        ui_interface = UiInterFace()
        ui_interface.ui.show()
        sys.exit(app.exec())
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
