# 2. 解决方案架构与创新文档

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | 2025-08-12 11:45:00 +08:00 | BA | 初始创建 |
| 1.1  | 2025-08-12 11:58:26 +08:00 | BA | 完成方案B优化设计和用户沟通确认 |

---

## 0. 方案设计过程记录

### 0.1 多轮沟通记录
**第一轮沟通 - 方案介绍和初步反馈**
- 提出了3个解决方案：渐进式扩展、重新设计UI、模块化架构
- 用户选择方案B（重新设计UI方案）
- 用户明确要求使用PyQt5而非PySide6
- 用户希望UI通过代码实现

**第二轮沟通 - 深入讨论和关切解答**
- 分析了PyQt5 vs PySide6的技术差异
- 设计了详细的UI布局和代码架构
- 制定了5周的开发计划

**第三轮沟通 - 方案细化和最终确认**
- 确认UI风格：实用简洁型
- 确认显示精度：小数点后三位
- 确认开发优先级：核心功能优先，配置保存/加载和状态指示后续开发
- 最终方案获得用户确认

### 0.2 方案选择决策记录
| 方案 | 评估结果 | 用户决策 | 决策理由 |
| :--- | :--- | :--- | :--- |
| 方案A: 渐进式扩展 | 推荐 | 未选择 | 用户更重视用户体验 |
| 方案B: 重新设计UI | 平衡 | ✅ 选择 | 平衡了开发成本和用户体验 |
| 方案C: 模块化架构 | 长期最优 | 未选择 | 开发周期过长 |

## 1. 最终解决方案概述

### 1.1 解决方案定义
**方案名称**: PyQt5重新设计UI方案
**核心理念**: 保持现有ZMotion后端控制逻辑，完全重新设计5轴运动控制用户界面

### 1.2 技术选型决策

#### PyQt5 vs PySide6 技术对比
| 对比维度 | PyQt5 | PySide6 | 选择理由 |
|---------|-------|---------|----------|
| **许可证** | GPL/商业双许可 | LGPL (更宽松) | 项目为内部使用，GPL可接受 |
| **API兼容性** | 成熟稳定 | 基本兼容，少量差异 | PyQt5文档和示例更丰富 |
| **社区支持** | 非常成熟 | 快速发展中 | 更多参考资源和解决方案 |
| **迁移成本** | 需要适配 | 无需迁移 | 迁移成本可控，收益明显 |

**最终选择**: PyQt5
**选择依据**: 用户明确要求 + 更丰富的社区资源 + 满足项目需求

### 1.3 解决方案优势
- ✅ **用户体验优化**: 专门为5轴控制设计的界面布局
- ✅ **技术风险可控**: 基于成熟的PyQt5技术栈
- ✅ **开发周期合理**: 5周开发周期，平衡质量和效率
- ✅ **代码可维护**: 模块化设计，职责分离清晰
- ✅ **后端复用**: 最大化复用现有ZMotion控制逻辑

## 2. 系统架构设计

### 2.1 整体架构图
```
┌─────────────────────────────────────────────────────────┐
│                    应用层 (PyQt5 UI)                    │
├─────────────────────────────────────────────────────────┤
│  主窗口     │  参数配置   │  状态显示   │  控制组件    │
│  MainWindow │  ConfigDialog│ StatusPanel │ ControlPanel │
├─────────────────────────────────────────────────────────┤
│                    业务逻辑层                            │
├─────────────────────────────────────────────────────────┤
│  运动控制器  │  轴管理器   │  配置管理器  │  状态管理器  │
│ MotionCtrl  │ AxisManager │ ConfigMgr   │ StatusMgr   │
├─────────────────────────────────────────────────────────┤
│                    硬件接口层                            │
├─────────────────────────────────────────────────────────┤
│              ZMotion控制器接口 (复用现有)                │
│                zmcdll.zauxdllPython                     │
├─────────────────────────────────────────────────────────┤
│                    硬件层                               │
├─────────────────────────────────────────────────────────┤
│                  ZMotion控制器                          │
│              5轴运动控制硬件系统                         │
└─────────────────────────────────────────────────────────┘
```

### 2.2 文件结构设计
```
zmotiondof5/
├── main.py                 # 应用程序入口
├── ui/                     # UI层
│   ├── __init__.py
│   ├── main_window.py      # 主窗口类
│   ├── config_dialog.py    # 参数配置对话框
│   └── widgets/            # UI组件
│       ├── __init__.py
│       ├── axis_control.py # 轴控制组件
│       ├── status_display.py # 状态显示组件
│       └── connection_panel.py # 连接面板组件
├── core/                   # 业务逻辑层
│   ├── __init__.py
│   ├── motion_controller.py # 运动控制核心逻辑
│   ├── axis_manager.py     # 轴管理器
│   └── config_manager.py   # 配置管理器
├── zmcdll/                 # 硬件接口层(复用现有)
│   ├── __init__.py
│   ├── zauxdllPython.py
│   └── zauxdll.dll
└── resources/              # 资源文件
    ├── icons/              # 图标资源
    └── styles/             # 样式文件(可选)
```

### 2.3 核心类设计

#### 主窗口类 (MainWindow)
```python
class MainWindow(QMainWindow):
    """5轴运动控制主窗口"""
    def __init__(self):
        super().__init__()
        self.motion_controller = MotionController()
        self.axis_manager = AxisManager()
        self.setup_ui()
        self.setup_connections()
        self.setup_timer()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央窗口部件
        # 创建连接控制区域
        # 创建位置显示区域
        # 创建轴控制区域
        # 创建单步控制区域
        
    def setup_connections(self):
        """设置信号连接"""
        # 连接按钮信号
        # 连接定时器信号
        
    def setup_timer(self):
        """设置状态更新定时器"""
        # 100ms更新频率
```

#### 轴管理器 (AxisManager)
```python
class AxisManager:
    """5轴管理器"""
    def __init__(self):
        self.axes = {
            'X': {'type': 'linear', 'unit': 'mm', 'precision': 3, 'axis_num': 0},
            'Y': {'type': 'linear', 'unit': 'mm', 'precision': 3, 'axis_num': 1},
            'Z': {'type': 'linear', 'unit': 'mm', 'precision': 3, 'axis_num': 2},
            'Rx': {'type': 'rotary', 'unit': '°', 'precision': 3, 'axis_num': 3},
            'Ry': {'type': 'rotary', 'unit': '°', 'precision': 3, 'axis_num': 4}
        }
        
    def get_axis_position(self, axis_name):
        """获取轴位置"""
        
    def move_axis_to_position(self, axis_name, position):
        """移动轴到指定位置"""
        
    def move_all_axes_to_positions(self, positions):
        """多轴协调运动"""
        
    def home_all_axes(self):
        """所有轴回零"""
```

## 3. 用户界面设计

### 3.1 主窗口布局设计
```
┌─────────────────────────────────────────────────────────┐
│                    5轴运动控制系统                        │
├─────────────────────────────────────────────────────────┤
│  连接控制区域                                            │
│  IP地址: [************* ▼] [扫描] [连接] [断开]          │
│  状态: [已连接] 控制器: [ZMotion-MC4000]                 │
├─────────────────────────────────────────────────────────┤
│  位置信息显示区域 (实时更新)                              │
│  X: 5.200mm   Y: 3.100mm   Z: 1.500mm                  │
│  Rx: 0.500°   Ry: 0.300°                               │
├─────────────────────────────────────────────────────────┤
│  轴控制区域                                              │
│  目标位置设置:                                           │
│  X: [_____] mm  Y: [_____] mm  Z: [_____] mm            │
│  Rx: [_____] °  Ry: [_____] °                           │
│  [运动到目标位置] [停止运动] [复位(回零)]                 │
├─────────────────────────────────────────────────────────┤
│  单步控制区域                                            │
│  步长设置:                                               │
│  X: [0.001] mm  Y: [0.001] mm  Z: [0.001] mm            │
│  Rx: [0.001] °  Ry: [0.001] °                           │
│                                                         │
│  X轴: [+] [-]  Y轴: [+] [-]  Z轴: [+] [-]                │
│  Rx轴: [+] [-]  Ry轴: [+] [-]                           │
├─────────────────────────────────────────────────────────┤
│  [参数配置] [关于] [帮助]                                 │
└─────────────────────────────────────────────────────────┘
```

### 3.2 参数配置对话框设计
```
┌─────────────────────────────────────────┐
│            轴参数配置                    │
├─────────────────────────────────────────┤
│ 选择轴: [X轴 ▼]                         │
├─────────────────────────────────────────┤
│ UNITS (脉冲当量):    [1000.000]         │
│ 起始速度 (mm/s):     [0.000   ]         │
│ 运行速度 (mm/s):     [10.000  ]         │
│ 加速度 (mm/s²):      [100.000 ]         │
│ 减速度 (mm/s²):      [100.000 ]         │
│ S曲线时间 (ms):      [20.000  ]         │
├─────────────────────────────────────────┤
│ [应用到轴] [重置为默认值]                │
│ [确定] [取消]                           │
└─────────────────────────────────────────┘
```

### 3.3 UI设计原则
- **实用简洁**: 界面布局清晰，功能分区明确
- **信息层次**: 重要信息突出显示，次要信息适当弱化
- **操作便捷**: 常用功能一键操作，减少操作步骤
- **状态反馈**: 实时显示系统状态和操作结果
- **错误处理**: 友好的错误提示和异常处理

## 4. 核心功能实现方案

### 4.1 连接管理功能
**功能描述**: 管理与ZMotion控制器的IP连接
**实现方案**:
- 复用现有的IP扫描和连接逻辑
- 优化连接状态显示和错误处理
- 添加自动重连机制

**关键代码结构**:
```python
class ConnectionManager:
    def scan_controllers(self):
        """扫描可用的控制器"""
        
    def connect_to_controller(self, ip_address):
        """连接到指定IP的控制器"""
        
    def disconnect_from_controller(self):
        """断开控制器连接"""
        
    def get_connection_status(self):
        """获取连接状态"""
```

### 4.2 5轴状态监测功能
**功能描述**: 实时显示5个轴的位置、状态和速度
**实现方案**:
- 100ms定时器更新轴状态
- 显示精度: 小数点后3位
- 状态包括: 位置、运动状态、当前速度

**关键代码结构**:
```python
class StatusMonitor:
    def __init__(self):
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(100)  # 100ms更新
        
    def update_status(self):
        """更新所有轴状态"""
        for axis_name in ['X', 'Y', 'Z', 'Rx', 'Ry']:
            position = self.get_axis_position(axis_name)
            self.update_position_display(axis_name, position)
```

### 4.3 多轴协调运动功能
**功能描述**: 5个轴同时运动到目标位置
**实现方案**:
- 使用ZMotion的多轴直线插补功能
- 支持运动过程中的停止操作
- 运动完成状态反馈

**关键代码结构**:
```python
class MultiAxisController:
    def move_to_positions(self, target_positions):
        """多轴协调运动到目标位置"""
        axis_list = [0, 1, 2, 3, 4]  # X, Y, Z, Rx, Ry
        position_list = [
            target_positions['X'],
            target_positions['Y'], 
            target_positions['Z'],
            target_positions['Rx'],
            target_positions['Ry']
        ]
        return self.zmc.ZAux_Direct_MoveAbs(5, axis_list, position_list)
```

### 4.4 单轴单步运动功能
**功能描述**: 每个轴的正向和负向单步运动
**实现方案**:
- 可配置的步长设置
- 立即执行的单步运动
- 支持连续点击操作

**关键代码结构**:
```python
class SingleStepController:
    def step_move(self, axis_name, direction, step_size):
        """单轴单步运动"""
        axis_num = self.axis_manager.get_axis_number(axis_name)
        current_pos = self.get_current_position(axis_num)
        target_pos = current_pos + (step_size * direction)
        return self.zmc.ZAux_Direct_Single_MoveAbs(axis_num, target_pos)
```

### 4.5 轴回零功能
**功能描述**: 所有轴回到零位置
**实现方案**:
- 一键回零操作
- 回零过程状态显示
- 回零完成确认

## 5. 技术实现细节

### 5.1 PyQt5迁移方案
**迁移步骤**:
1. 更新导入语句: `from PySide6.QtWidgets` → `from PyQt5.QtWidgets`
2. 更新UI加载方式: `QUiLoader` → `uic.loadUi` 或纯代码实现
3. 适配API差异: 主要是信号连接语法的微调
4. 测试验证: 确保所有功能正常工作

**关键差异处理**:
```python
# PySide6 方式
from PySide6.QtWidgets import QApplication
from PySide6.QtUiTools import QUiLoader

# PyQt5 方式  
from PyQt5.QtWidgets import QApplication
from PyQt5 import uic
```

### 5.2 精度控制实现
**显示精度**: 小数点后3位
**实现方案**:
```python
def format_position(self, value, precision=3):
    """格式化位置显示"""
    return f"{value:.{precision}f}"

def format_linear_position(self, value):
    """格式化线性轴位置 (mm)"""
    return f"{value:.3f} mm"
    
def format_rotary_position(self, value):
    """格式化旋转轴位置 (°)"""
    return f"{value:.3f}°"
```

### 5.3 错误处理和异常管理
**错误处理策略**:
- 连接异常: 自动重试 + 用户提示
- 运动异常: 立即停止 + 状态恢复
- 参数异常: 输入验证 + 范围检查
- 系统异常: 日志记录 + 优雅降级

## 6. 开发计划和里程碑

### 6.1 详细开发计划 (5周)

#### Week 1: 环境准备和架构搭建
**目标**: 完成开发环境配置和基础架构
- Day 1-2: PyQt5环境配置和项目结构创建
- Day 3-4: 现有代码分析和接口提取
- Day 5: 核心类框架搭建和基础测试

#### Week 2: 核心UI开发 (第一阶段)
**目标**: 完成主窗口和基础UI组件
- Day 1-2: 主窗口布局设计和实现
- Day 3-4: 连接管理界面和功能
- Day 5: 位置显示组件开发

#### Week 3: 核心UI开发 (第二阶段)
**目标**: 完成轴控制和单步控制功能
- Day 1-2: 5轴控制组件开发
- Day 3-4: 单步控制组件开发
- Day 5: 多轴协调运动功能集成

#### Week 4: 参数配置和高级功能
**目标**: 完成参数配置功能
- Day 1-2: 参数配置对话框开发
- Day 3-4: 配置管理和验证逻辑
- Day 5: 回零功能和状态管理

#### Week 5: 集成测试和优化
**目标**: 完成系统集成和质量优化
- Day 1-2: 功能集成测试和bug修复
- Day 3-4: UI交互优化和性能调优
- Day 5: 文档编写和交付准备

### 6.2 关键里程碑
- ✅ **里程碑1** (Week 1): 开发环境就绪，架构确定
- ⏳ **里程碑2** (Week 2): 基础UI完成，连接功能可用
- ⏳ **里程碑3** (Week 3): 核心控制功能完成
- ⏳ **里程碑4** (Week 4): 参数配置功能完成
- ⏳ **里程碑5** (Week 5): 系统集成完成，可交付

## 7. 风险评估和应对策略

### 7.1 技术风险
| 风险项 | 风险等级 | 影响 | 应对策略 |
|--------|----------|------|----------|
| PyQt5迁移兼容性 | 中 | 开发延期 | 提前验证关键API，准备回退方案 |
| 多轴控制稳定性 | 中 | 功能异常 | 充分测试，分阶段验证 |
| UI性能问题 | 低 | 用户体验 | 性能监控，及时优化 |

### 7.2 进度风险
| 风险项 | 风险等级 | 影响 | 应对策略 |
|--------|----------|------|----------|
| 需求变更 | 中 | 开发延期 | 需求冻结，变更控制 |
| 技术难点 | 中 | 开发延期 | 技术预研，专家支持 |
| 测试时间不足 | 低 | 质量风险 | 并行开发测试，自动化测试 |

## 8. 质量保证计划

### 8.1 代码质量标准
- **编码规范**: 遵循PEP 8和项目编码规范
- **代码审查**: 关键模块必须进行代码审查
- **单元测试**: 核心逻辑单元测试覆盖率>80%
- **集成测试**: 完整的功能集成测试

### 8.2 用户体验标准
- **响应时间**: UI操作响应时间<200ms
- **稳定性**: 连续运行8小时无异常
- **易用性**: 新用户5分钟内掌握基本操作
- **错误处理**: 友好的错误提示和恢复机制

## 9. 递进输出 (为模式3技术架构设计提供解决方案基础)

### 9.1 为SA角色提供的关键信息
| 输出类别 | 具体内容 | 技术架构影响 | 优先级 |
| :--- | :--- | :--- | :--- |
| 技术选型 | PyQt5 + ZMotion后端架构 | 技术栈确定和依赖管理 | 高 |
| 系统架构 | 三层架构设计(UI/业务/硬件) | 模块划分和接口设计 | 高 |
| UI设计方案 | 实用简洁的5轴控制界面 | 前端技术选型和组件设计 | 高 |
| 功能模块 | 连接、监测、控制、配置四大模块 | 系统组件设计和集成方案 | 高 |
| 性能要求 | 100ms状态更新，200ms响应时间 | 性能优化和架构设计 | 中 |
| 开发约束 | 5周开发周期，代码复用优先 | 技术选择和实现策略 | 中 |

### 9.2 解决方案传递检查清单
- [x] 技术选型决策明确且有依据
- [x] 系统架构设计完整且可行
- [x] UI设计方案详细且符合用户需求
- [x] 功能实现方案具体且可操作
- [x] 开发计划合理且可执行
- [x] 风险评估全面且有应对策略
- [x] 质量标准明确且可衡量
- [x] 用户沟通充分且需求确认

### 9.3 后续阶段准备
* **技术架构设计准备**: 为SA角色提供完整的解决方案基础，支持详细的技术栈选择和系统架构设计
* **技术选型依据**: 为技术组件选择提供明确的约束条件和性能要求
* **实现策略指导**: 为具体的技术实现提供方案框架和设计原则

**递进关系说明**: 本文档作为模式2的产出，基于模式1的需求分析结果，为模式3的技术栈确定和系统架构设计提供完整的解决方案基础，确保SA角色能够基于确定的解决方案进行技术架构设计。