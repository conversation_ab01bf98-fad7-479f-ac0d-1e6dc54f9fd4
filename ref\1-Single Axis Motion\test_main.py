#!/usr/bin/python
# coding:utf-8

"""
测试版本的main.py - 用于调试Qt线程问题
"""
import sys
import os
from PySide2.QtWidgets import QApplication
from PySide2.QtCore import QTimer

def test_qt_objects():
    """测试Qt对象创建"""
    print("Testing Qt objects creation...")
    
    # 测试QTimer创建
    try:
        timer = QTimer()
        print("✓ QTimer created successfully")
        timer.deleteLater()
    except Exception as e:
        print(f"✗ QTimer creation failed: {e}")
    
    # 测试导入ZAUXDLL
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), 'zmcdll'))
        from zauxdllPython import ZAUXDLL
        print("✓ ZAUXDLL imported successfully")
        
        # 测试ZAUXDLL创建
        zmc = ZAUXDLL()
        print("✓ ZAUXDLL created successfully")
    except Exception as e:
        print(f"✗ ZAUXDLL creation failed: {e}")

def test_ui_loading():
    """测试UI加载"""
    try:
        from PySide2.QtUiTools import QUiLoader
        from PySide2.QtCore import QFile
        
        print("Testing UI loading...")
        
        q_state_file = QFile("mainweiget.ui")
        if not q_state_file.exists():
            print("✗ UI file 'mainweiget.ui' not found")
            return
            
        q_state_file.open(QFile.ReadOnly)
        ui = QUiLoader().load(q_state_file, None)
        q_state_file.close()
        
        if ui:
            print("✓ UI loaded successfully")
            ui.deleteLater()
        else:
            print("✗ UI loading failed")
            
    except Exception as e:
        print(f"✗ UI loading failed: {e}")

def main():
    """主测试函数"""
    print("=== Qt Thread Issue Debug Test ===")
    print(f"Python version: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    
    try:
        # 创建QApplication
        app = QApplication([])
        print("✓ QApplication created successfully")
        
        # 测试Qt对象创建
        test_qt_objects()
        
        # 测试UI加载
        test_ui_loading()
        
        print("=== Test completed ===")
        
    except Exception as e:
        print(f"✗ Application creation failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
