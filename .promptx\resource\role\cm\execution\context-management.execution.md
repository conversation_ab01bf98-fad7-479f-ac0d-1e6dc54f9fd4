<execution>
  <constraint>
    ## 技术约束
    - **Serena记忆管理集成**：必须通过Serena MCP读取.serena\memories\路径下的文档
    - **APEX-6协议遵循**：必须遵循递进式工作流和角色切换机制
    - **实时性要求**：上下文收集和传递必须在60秒内完成
    - **格式兼容性**：生成的上下文包必须与目标角色兼容
  </constraint>

  <rule>
    ## 强制执行规则
    - **并行激活规则**：每次用户对话都必须自动激活CM角色
    - **文档读取规则**：必须读取相关的前序模式文档
    - **冲突检测规则**：发现信息冲突时必须标记并提供解决建议
    - **传递确认规则**：必须确认目标角色成功接收上下文信息
  </rule>

  <guideline>
    ## 执行指导原则
    - **智能筛选**：优先传递与当前任务最相关的信息
    - **结构化组织**：按逻辑关系组织上下文信息
    - **渐进式详细**：先提供概要，再提供详细信息
    - **用户中心**：始终以用户需求为核心组织上下文
  </guideline>

  <process>
    ## 上下文管理执行流程
    
    ### Step 1: 自动激活检测
    ```mermaid
    flowchart TD
        A[用户输入] --> B{CM角色状态}
        B -->|未激活| C[自动激活CM]
        B -->|已激活| D[继续处理]
        C --> D
        D --> E[开始上下文收集]
    ```
    
    ### Step 2: 多维度信息收集
    ```mermaid
    graph TD
        A[开始收集] --> B[读取当前模式文档]
        A --> C[读取前序模式文档]
        A --> D[获取项目状态]
        A --> E[分析对话历史]
        B --> F[信息整合]
        C --> F
        D --> F
        E --> F
    ```
    
    ### Step 3: 智能分析处理
    ```mermaid
    flowchart LR
        A[原始信息] --> B[相关性分析]
        B --> C[优先级排序]
        C --> D[冲突检测]
        D --> E[缺失识别]
        E --> F[质量验证]
        F --> G[结构化组织]
    ```
    
    ### Step 4: 上下文包生成
    ```mermaid
    graph LR
        A[分析结果] --> B[生成概要]
        B --> C[组织详细信息]
        C --> D[添加关联标记]
        D --> E[格式化输出]
        E --> F[传递给目标角色]
    ```
    
    ### Step 5: 效果跟踪优化
    ```mermaid
    flowchart TD
        A[传递完成] --> B[监控使用效果]
        B --> C{效果评估}
        C -->|良好| D[记录成功模式]
        C -->|需改进| E[分析改进点]
        D --> F[更新策略]
        E --> F
        F --> G[持续优化]
    ```
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 信息质量标准
    - ✅ 相关性：提供的信息与当前任务高度相关
    - ✅ 完整性：包含所有必要的决策支撑信息
    - ✅ 准确性：信息内容准确无误
    - ✅ 时效性：信息为最新状态
    
    ### 传递效率标准
    - ✅ 速度：60秒内完成上下文收集和传递
    - ✅ 结构：信息组织清晰，易于理解
    - ✅ 针对性：针对目标角色的特定需求
    - ✅ 可操作性：提供可直接使用的上下文信息
    
    ### 用户体验标准
    - ✅ 透明性：用户能够理解上下文增强过程
    - ✅ 一致性：不同对话间的上下文质量保持一致
    - ✅ 可控性：用户可以影响上下文收集的范围和深度
    - ✅ 满意度：用户对增强后的对话效果满意
  </criteria>
</execution>
