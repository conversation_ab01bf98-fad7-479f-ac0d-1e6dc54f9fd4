<thought>
  <exploration>
    ## 上下文需求分析维度
    
    ### 用户问题类型识别
    - **技术实现类**：代码开发、架构设计、技术选型
    - **需求分析类**：功能定义、用户故事、业务流程
    - **项目管理类**：任务规划、进度跟踪、资源分配
    - **问题诊断类**：错误排查、性能优化、系统调试
    
    ### 上下文范围评估
    - **当前对话范围**：本次对话的具体需求和背景
    - **项目历史范围**：相关的历史决策和实现
    - **技术栈范围**：涉及的技术组件和依赖关系
    - **业务逻辑范围**：相关的业务规则和约束条件
  </exploration>
  
  <reasoning>
    ## 上下文收集策略推理
    
    ### 信息源优先级排序
    ```
    1. 当前模式文档（最高优先级）
    2. 前序模式文档（递进依赖）
    3. 项目状态信息（实时状态）
    4. 历史对话记录（参考信息）
    ```
    
    ### 关联性分析逻辑
    - **直接相关**：与当前任务直接相关的信息
    - **间接相关**：可能影响决策的背景信息
    - **背景相关**：提供上下文理解的支撑信息
    - **结构相关**：项目架构和组织结构信息
  </reasoning>
  
  <challenge>
    ## 上下文质量挑战
    
    ### 信息完整性验证
    - 是否遗漏关键的前序决策？
    - 是否包含所有相关的技术约束？
    - 是否考虑了用户的具体需求背景？
    
    ### 信息冲突检测
    - 不同文档间是否存在矛盾？
    - 历史决策与当前需求是否一致？
    - 技术选型是否存在冲突？
    
    ### 时效性评估
    - 信息是否为最新状态？
    - 是否存在过时的决策或约束？
    - 项目状态是否发生重大变化？
  </challenge>
  
  <plan>
    ## 上下文增强执行计划
    
    ### Phase 1: 快速需求识别 (10秒)
    ```
    用户输入 → 问题类型分析 → 上下文范围评估 → 收集策略确定
    ```
    
    ### Phase 2: 多维度信息收集 (30秒)
    ```
    当前文档读取 → 前序文档分析 → 项目状态获取 → 关联信息整合
    ```
    
    ### Phase 3: 智能分析整合 (20秒)
    ```
    关联性分析 → 优先级排序 → 冲突检测 → 缺失识别
    ```
    
    ### Phase 4: 上下文包生成 (10秒)
    ```
    结构化组织 → 智能提示词生成 → 目标角色传递 → 效果跟踪
    ```
  </plan>
</thought>
