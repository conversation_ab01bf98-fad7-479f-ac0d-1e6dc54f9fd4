# 1. 需求与规范文档

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | 2025-01-12 15:30:00 | PM | 初始创建 |
| 1.1  | 2025-01-12 15:45:00 | PM | 完成5轴运动控制项目需求分析 |

---

## 0. 项目初始化状态

### 0.1 APEX-6强制初始化检查清单
- [x] PromptX中已创建CM角色并定制完成
- [x] PromptX中已创建PM角色并定制完成
- [x] PromptX中已创建BA角色并定制完成
- [x] PromptX中已创建SA角色并定制完成
- [x] PromptX中已创建PL角色并定制完成
- [x] PromptX中已创建LD角色并定制完成
- [x] 用户已在.serena\memories\路径下创建X.Continuous Context and Intelligence Document.md
- [x] 用户已在.serena\memories\路径下创建1.Requirements and Specifications Document.md
- [x] 用户已在.serena\memories\路径下创建2.Solution Architecture and Innovation Document.md
- [x] 用户已在.serena\memories\路径下创建3.Technology Stack and Design Document.md
- [x] 用户已在.serena\memories\路径下创建4.Project Planning and Task Management.md
- [x] 用户已在.serena\memories\路径下创建5.Development Progress and Testing.md

### 0.2 工具集成验证状态
- [x] ACE(AugmentContextEngine)信息收集工具可正常使用
- [x] Augment自带的task工具已激活并可进行项目管理
- [x] Serena MCP记忆管理工具连接正常，可读写.serena\memories\文档
- [x] PromptX MCP角色管理工具可正常切换6个专业角色
- [ ] Context 7 MCP专业库文档查询工具可正常使用
- [x] PowerShell环境验证通过，支持所有控制台操作
- [x] mcp.server_time时间服务可正常获取准确时间戳

## 1. 项目概述

### 1.1 项目目标
基于现有的单轴运动控制程序，开发一个功能完整的5轴运动控制系统，支持3个平移轴(X, Y, Z)和2个旋转轴(Rx, Ry)的精密控制，为工业自动化应用提供高精度的多轴协调运动控制解决方案。

### 1.2 项目范围
* **范围内 (In Scope):**
  * 基于现有ZMotion控制器架构的5轴运动控制系统
  * IP网络连接方式的控制器通信
  * 实时轴状态监测和显示功能
  * 独立的轴参数配置UI页面
  * 多轴协调运动控制功能
  * 单轴单步运动控制功能
  * 轴回零功能
  * 基于PySide6的图形用户界面
  * 代码复用和架构优化
* **范围外 (Out of Scope):**
  * 串口和PCI连接方式（仅支持IP连接）
  * 超过5轴的运动控制
  * 复杂的轨迹规划算法
  * 实时数据记录和分析功能
  * 网络安全和用户权限管理

## 2. 用户需求

### 2.1 用户故事 (User Stories)
| ID | 角色 | 我想要... (功能) | 以便... (价值) | 优先级 |
| :-- | :-- | :--- | :--- | :--- |
| US001 | 操作员 | 通过IP连接到ZMotion控制器 | 建立与运动控制系统的通信 | 高 |
| US002 | 操作员 | 实时查看5个轴的当前位置和状态 | 监控系统运行状态和轴位置 | 高 |
| US003 | 操作员 | 设置5个轴的目标位置并执行协调运动 | 实现精确的多轴定位 | 高 |
| US004 | 操作员 | 执行单轴的正向和负向单步运动 | 进行精细的位置调整 | 高 |
| US005 | 操作员 | 执行所有轴的回零操作 | 重置系统到初始状态 | 高 |
| US006 | 工程师 | 在独立页面配置轴参数 | 根据不同应用调整运动参数 | 中 |
| US007 | 操作员 | 设置每个轴的单步移动步长 | 控制单步运动的精度 | 中 |

### 2.2 功能性需求 (Functional Requirements)
* **FR001: 控制器连接管理**
  * **描述:** 系统支持通过IP地址连接到ZMotion控制器，包括IP扫描、连接建立、连接状态监控和断开连接功能
  * **规则:** 
    - 支持IP地址自动扫描和手动输入
    - 连接状态实时显示
    - 连接失败时提供明确的错误提示
    - 支持连接重试机制

* **FR002: 轴状态监测和显示**
  * **描述:** 实时显示5个轴(X, Y, Z, Rx, Ry)的当前位置、运动状态和速度信息
  * **规则:**
    - 位置显示精度：平移轴精确到0.01mm，旋转轴精确到0.01°
    - 状态更新频率：100ms
    - 显示内容包括：当前位置、运动状态(停止/运动)、当前速度

* **FR003: 多轴协调运动控制**
  * **描述:** 支持设置5个轴的目标位置，执行多轴同步运动到目标位置
  * **规则:**
    - 支持绝对位置设定
    - 多轴运动轨迹同步
    - 运动过程中可以停止
    - 运动完成后状态反馈

* **FR004: 单轴单步运动控制**
  * **描述:** 支持每个轴的正向和负向单步运动，步长可配置
  * **规则:**
    - 每个轴独立的步长设置
    - 正向和负向运动按钮
    - 单步运动立即执行
    - 步长范围：0.001-100mm(平移轴)，0.001-10°(旋转轴)

* **FR005: 轴回零功能**
  * **描述:** 支持所有轴同时回到零位置
  * **规则:**
    - 一键回零操作
    - 回零过程状态显示
    - 回零完成确认

* **FR006: 轴参数配置**
  * **描述:** 提供独立的配置界面设置轴运动参数
  * **规则:**
    - 参数包括：UNITS、起始速度、运行速度、加速度、减速度、S曲线
    - 每个轴独立配置
    - 参数实时生效
    - 参数验证和范围检查

### 2.3 非功能性需求 (Non-Functional Requirements)
* **性能:** 
  - 轴状态更新频率：100ms
  - 用户操作响应时间：<200ms
  - 多轴运动启动延迟：<500ms
* **安全性:** 
  - 运动范围限制检查
  - 急停功能支持
  - 参数合法性验证
* **可用性:** 
  - 界面布局清晰，操作直观
  - 支持Windows 10/11操作系统
  - 错误信息明确易懂
* **可靠性:** 
  - 通信异常自动重连
  - 运动过程异常处理
  - 系统稳定运行时间>8小时

## 3. 系统规范

### 3.1 编码规范 (遵循APEX-6核心编码原则)
* **核心编码原则:** KISS, YAGNI, SOLID, DRY, 高内聚低耦合, 代码可读性, 可测试性, 安全编码
* **主要语言:** Python 3.8+
* **风格指南:** PEP 8
* **命名约定:** 
  - 变量和函数使用snake_case
  - 类名使用PascalCase
  - 常量使用UPPER_CASE
  - 私有成员使用下划线前缀
* **代码注释:** 
  - 函数和类必须有docstring
  - 复杂逻辑必须有行内注释
  - 注释覆盖率>30%
* **CHENGQI注释格式:**
  ```python
  # {{CHENGQI: Action: [Added/Modified/Removed]; Timestamp: [YYYY-MM-DD HH:MM:SS +08:00]; Reason: [Task ID: #123, brief why]; Principle_Applied: [SOLID/Security/etc]; Role: [CM/PM/BA/SA/PL/LD]; Mode: [MODEX,1-5];}}
  ```

### 3.2 开发规范
* **版本控制规范:**
  * 分支策略: Git Flow
  * 提交信息格式: Conventional Commits
  * 代码审查要求: 必须
* **测试规范:**
  * 单元测试覆盖率要求: >80%
  * 测试命名约定: test_[function_name]_[scenario]
  * 集成测试: 控制器通信和UI交互测试
* **质量标准:**
  * 代码复杂度限制: 圈复杂度<10
  * 性能要求: UI响应时间<200ms
  * 安全编码规范: 输入验证、异常处理、资源管理

### 3.3 项目管理规范 (遵循APEX-6工作流)
* **递进式工作流原则:** 后续模式必须基于前序模式产出的文档开展工作，确保工作流的连贯性和一致性
* **文档强制维护原则:** 每次开发活动都必须同步更新Serena中的相关文档，不得遗漏
* **需求确认原则:** 每次用户提问后，必须先复述理解的需求，提出解决方案，获得用户确认后再执行
* **多轮沟通机制:** 特别在模式2方案细化阶段，需要与用户进行充分的多轮深度沟通
* **角色切换规范:** 每种模式切换时必须同步切换到对应的专业角色
* **工具集成要求:**
  - 强制使用ACE进行信息收集
  - 使用Augment task工具进行项目规划与追踪
  - 使用Serena MCP进行文档读写操作
  - 使用PromptX MCP进行角色切换
  - 模式5中使用专业库时必须先使用Context 7 MCP查询文档
* **PowerShell指令要求:** 所有控制台操作必须使用Windows PowerShell准确指令
* **时间戳管理:** 所有文件操作必须使用mcp.server_time服务获取准确时间戳

### 3.4 验收标准
* **功能验收:** 
  - 所有用户故事通过验收测试
  - 5轴运动控制功能正常
  - UI界面操作流畅
  - 参数配置功能完整
* **质量验收:** 
  - 代码覆盖率>80%
  - 性能指标达标
  - 安全性测试通过
  - 代码审查通过
* **文档验收:** 
  - 用户手册完整
  - API文档准确
  - 代码注释充分

## 4. 技术约束和参考架构

### 4.1 基础架构约束
* **基础代码库:** `d:\code\zmotiondof5\ref\1-Single Axis Motion\`
* **控制器接口:** ZMotion控制器Python接口库(zmcdll)
* **GUI框架:** PySide6 (基于现有架构)
* **通信方式:** 仅支持IP连接
* **操作系统:** Windows 10/11

### 4.2 现有代码分析
* **主要文件:**
  - `main.py`: 应用程序入口
  - `Ui_Weiget.py`: 主要UI逻辑类
  - `mainweiget.ui`: Qt Designer UI文件
  - `zmcdll/zauxdllPython.py`: ZMotion控制器接口
* **可复用组件:**
  - 控制器连接管理
  - 轴状态监测机制
  - 参数设置逻辑
  - UI基础框架

### 4.3 扩展要求
* **轴数量扩展:** 从1轴扩展到5轴
* **UI界面重新设计:** 适应5轴显示和控制
* **多轴协调控制:** 新增多轴同步运动功能
* **配置页面:** 新增独立的参数配置界面

## 5. 递进输出 (为模式2方案细化提供需求基础)

### 5.1 为BA角色提供的关键信息
| 输出类别 | 具体内容 | 方案设计影响 | 优先级 |
| :--- | :--- | :--- | :--- |
| 项目目标 | 5轴运动控制系统开发 | 技术方案选择和架构设计 | 高 |
| 功能需求 | 连接、监测、控制、配置四大功能模块 | 系统模块划分和接口设计 | 高 |
| 非功能需求 | 性能、安全、可用性、可靠性要求 | 技术选型和实现策略 | 高 |
| 技术约束 | 基于现有PySide6+ZMotion架构 | 技术栈选择和代码复用策略 | 高 |
| UI需求 | 5轴显示、多轴控制、独立配置页面 | UI架构设计和用户体验设计 | 中 |

### 5.2 需求传递检查清单
- [x] 项目目标明确且可衡量
- [x] 功能需求完整且无歧义
- [x] 非功能需求具体且可验证
- [x] 开发规范详细且可执行
- [x] 验收标准清晰且可操作
- [x] 用户期望和约束条件已明确记录
- [x] 技术约束和现有架构分析完成
- [x] UI需求和交互逻辑明确

### 5.3 后续阶段准备
* **方案设计准备:** 为BA角色提供完整的需求分析基础，支持多方案设计和对比分析
* **用户沟通准备:** 为多轮沟通提供需求背景和用户期望信息
* **技术约束传递:** 为技术方案选择提供明确的约束条件和质量要求
* **架构设计基础:** 为SA角色提供技术选型和系统架构设计的需求依据

**递进关系说明:** 本文档作为模式1的产出，为模式2的方案细化与多轮沟通提供完整的需求基础，确保BA角色能够基于准确的需求信息进行解决方案设计。